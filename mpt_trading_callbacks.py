"""
MPT Trading Callbacks Module
Handles all trading-related callbacks for the MPT application
"""

from app import app
import dash
from dash import Output, Input, State, callback_context
from dash.exceptions import PreventUpdate
import json
from mt5_trading import mpt_trader
import logging

# Configure logging
logger = logging.getLogger(__name__)


@app.callback(
    Output('account-info-display', 'children'),
    [Input('account-interval', 'n_intervals')]
)
def update_account_info(n_intervals):
    """
    Update account information display every 1 second
    
    Args:
        n_intervals: Number of intervals passed
        
    Returns:
        HTML div with account information
    """
    try:
        account_info = mpt_trader.get_account_info()
        
        if account_info:
            return [
                f"Server: {account_info['server']}",
                f"Balance: {account_info['balance']:.2f} {account_info['currency']}",
                f"Profit: {account_info['profit']:.2f} {account_info['currency']}",
                f"Equity: {account_info['equity']:.2f} {account_info['currency']}",
                f"Free Margin: {account_info['free_margin']:.2f} {account_info['currency']}"
            ]
        else:
            return "Account info unavailable - Check MT5 connection"
            
    except Exception as e:
        logger.error(f"Error updating account info: {str(e)}")
        return f"Error: {str(e)}"


@app.callback(
    [Output('trading-status-display', 'children'),
     Output('confirm-send-trades', 'displayed')],
    [Input('send-trades-mt5', 'n_clicks')],
    [State('clicked-portfolio-store', 'data'),
     State('trading-lot-size', 'value')]
)
def handle_send_trades_button(n_clicks, clicked_portfolio_data, lot_size):
    """
    Handle the Send Trades to MT5 button click
    
    Args:
        n_clicks: Number of button clicks
        clicked_portfolio_data: Data from the clicked portfolio (red circle)
        lot_size: Total lot size for trading
        
    Returns:
        Tuple of (status_message, show_confirmation_dialog)
    """
    if n_clicks == 0:
        raise PreventUpdate
    
    # Show confirmation dialog
    return "Waiting for confirmation...", True


@app.callback(
    Output('trading-status-display', 'children', allow_duplicate=True),
    [Input('confirm-send-trades', 'submit_n_clicks')],
    [State('clicked-portfolio-store', 'data'),
     State('trading-lot-size', 'value')],
    prevent_initial_call=True
)
def execute_trades_after_confirmation(submit_n_clicks, clicked_portfolio_data, lot_size):
    """
    Execute trades after user confirms
    
    Args:
        submit_n_clicks: Number of confirmation dialog submits
        clicked_portfolio_data: Data from the clicked portfolio (red circle)
        lot_size: Total lot size for trading
        
    Returns:
        Status message with trade results
    """
    if submit_n_clicks is None or submit_n_clicks == 0:
        raise PreventUpdate
    
    try:
        # Check if we have portfolio data
        if not clicked_portfolio_data:
            return "Error: No portfolio selected. Please click on a portfolio point (red circle) in the MPT chart first."
        
        # Extract portfolio weights from clicked data
        portfolio_weights = {}

        # The clicked_portfolio_data should contain the portfolio allocation
        # Format: {'combo': ['EURUSD', 'GBPUSD'], 'weights': [0.4, 0.3], ...}
        if 'combo' in clicked_portfolio_data and 'weights' in clicked_portfolio_data:
            symbols = clicked_portfolio_data['combo']
            weights = clicked_portfolio_data['weights']

            if len(symbols) == len(weights):
                portfolio_weights = dict(zip(symbols, weights))
            else:
                return "Error: Mismatch between symbols and weights in portfolio data."

        elif 'weights' in clicked_portfolio_data and isinstance(clicked_portfolio_data['weights'], dict):
            # Direct weights dictionary format
            portfolio_weights = clicked_portfolio_data['weights']

        elif 'allocation' in clicked_portfolio_data:
            # Parse allocation string if it's in string format
            allocation_str = clicked_portfolio_data['allocation']
            try:
                # Parse allocation string like "EURUSD:0.4, GBPUSD:0.3"
                pairs = allocation_str.split(',')
                for pair in pairs:
                    if ':' in pair:
                        symbol, weight = pair.strip().split(':')
                        portfolio_weights[symbol.strip()] = float(weight.strip())
            except Exception as e:
                return f"Error parsing portfolio allocation: {str(e)}"
        else:
            return "Error: Invalid portfolio data format. Please select a portfolio from the MPT chart."
        
        if not portfolio_weights:
            return "Error: No portfolio weights found. Please select a portfolio from the MPT chart."

        # Validate lot size
        if lot_size is None or lot_size <= 0:
            lot_size = 1.0

        # Add debug information
        debug_info = [
            f"📊 Portfolio Data Debug:",
            f"Selected Portfolio: {clicked_portfolio_data.get('optimization', 'Unknown')}",
            f"Symbols: {list(portfolio_weights.keys())}",
            f"Weights: {list(portfolio_weights.values())}",
            f"Total Lot Size: {lot_size}",
            ""
        ]

        logger.info(f"Executing trades with portfolio: {portfolio_weights}, lot size: {lot_size}")
        
        # Execute trades
        result = mpt_trader.send_mpt_portfolio_to_mt5(
            portfolio_weights=portfolio_weights,
            total_lot_size=lot_size,
            close_existing=True
        )
        
        # Format result message
        if result['success']:
            status_lines = debug_info + [
                f"✅ Trading completed successfully!",
                f"Strategy: {result['strategy']}",
                f"Summary: {result['summary']}",
                "",
                "Trade Details:"
            ]
            
            for trade in result['trades']:
                if trade['success']:
                    status_lines.append(
                        f"✅ {trade['symbol']}: {trade['action']} {trade['volume']:.2f} lots at {trade['price']:.5f}"
                    )
                else:
                    status_lines.append(
                        f"❌ {trade['symbol']}: {trade['error']}"
                    )
            
            if result['errors']:
                status_lines.append("")
                status_lines.append("Errors:")
                for error in result['errors']:
                    status_lines.append(f"❌ {error}")
        else:
            status_lines = debug_info + [
                f"❌ Trading failed!",
                f"Error: {result.get('error', 'Unknown error')}",
                "",
                "Trade Details:"
            ]
            
            for trade in result.get('trades', []):
                if trade['success']:
                    status_lines.append(
                        f"✅ {trade['symbol']}: {trade['action']} {trade['volume']:.2f} lots"
                    )
                else:
                    status_lines.append(
                        f"❌ {trade['symbol']}: {trade['error']}"
                    )
        
        return status_lines
        
    except Exception as e:
        logger.error(f"Error executing trades: {str(e)}")
        return f"❌ Error executing trades: {str(e)}"


@app.callback(
    [Output('trading-status-display', 'children', allow_duplicate=True),
     Output('confirm-close-positions', 'displayed')],
    [Input('close-all-positions', 'n_clicks')],
    prevent_initial_call=True
)
def handle_close_positions_button(n_clicks):
    """
    Handle the Close All Positions button click
    
    Args:
        n_clicks: Number of button clicks
        
    Returns:
        Tuple of (status_message, show_confirmation_dialog)
    """
    if n_clicks == 0:
        raise PreventUpdate
    
    # Show confirmation dialog
    return "Waiting for confirmation to close all positions...", True


@app.callback(
    Output('trading-status-display', 'children', allow_duplicate=True),
    [Input('confirm-close-positions', 'submit_n_clicks')],
    prevent_initial_call=True
)
def close_positions_after_confirmation(submit_n_clicks):
    """
    Close all positions after user confirms
    
    Args:
        submit_n_clicks: Number of confirmation dialog submits
        
    Returns:
        Status message with closing results
    """
    if submit_n_clicks is None or submit_n_clicks == 0:
        raise PreventUpdate
    
    try:
        logger.info("Closing all MT5 positions")
        
        # Close all positions
        result = mpt_trader._close_all_positions()
        
        # Format result message
        status_lines = [
            f"Position Closing Results:",
            f"Closed: {result['closed']} positions"
        ]
        
        if result['errors']:
            status_lines.append("")
            status_lines.append("Errors:")
            for error in result['errors']:
                status_lines.append(f"❌ {error}")
        else:
            status_lines.append("✅ All positions closed successfully!")
        
        return status_lines
        
    except Exception as e:
        logger.error(f"Error closing positions: {str(e)}")
        return f"❌ Error closing positions: {str(e)}"


# Initialize trading status
@app.callback(
    Output('trading-status-display', 'children', allow_duplicate=True),
    [Input('account-interval', 'n_intervals')],
    prevent_initial_call=True
)
def initialize_trading_status(n_intervals):
    """
    Initialize trading status display on first load
    """
    if n_intervals == 1:  # Only on first interval
        return "Ready for trading. Select a portfolio from the MPT chart and click 'Send Trades to MT5'."
    
    raise PreventUpdate
